'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import classNames from '@utils/classNames';
import styles from './ImageWithBlurPreview.module.css';

export default function ImageWithBlurPreview({
  data,
  mobileData,
  width,
  height,
  fill,
  quality,
  priority,
  unoptimized,
  loading,
  mainClass,
  prefferedSize,
  mobileObjectFit = 'contain', // New prop to control mobile image display
}: {
  data: any;
  mobileData?: any;
  width?: any;
  height?: any;
  fill?: any;
  unoptimized?: any;
  quality?: any;
  priority?: any;
  loading?: any;
  mainClass: any;
  prefferedSize?: 'large' | 'medium' | 'small';
  mobileObjectFit?: 'contain' | 'cover' | 'fill';
}) {
  const [isDesktopImageLoaded, setIsDesktopImageLoaded] = useState(false);
  const [isMobileImageLoaded, setIsMobileImageLoaded] = useState(false);

  // Debug logging to track which images are being used
  console.log('=== ImageWithBlurPreview Debug ===');
  console.log('Desktop data:', data);
  console.log('Mobile data:', mobileData);
  console.log('Preferred size:', prefferedSize);
  console.log('Fill prop:', fill);
  console.log('Main class:', mainClass);

  // Helper function to get the correct image URL based on preferred size
  const getImageUrl = (imageData: any, isMobile: boolean = false) => {
    if (!imageData) {
      console.log(
        `No image data available for ${isMobile ? 'mobile' : 'desktop'}`,
      );
      return '';
    }

    let selectedUrl = '';

    // For mobile devices, prefer smaller formats to reduce bandwidth and improve performance
    if (isMobile) {
      selectedUrl =
        imageData?.formats?.medium?.url ||
        imageData?.format?.medium?.url ||
        imageData?.formats?.small?.url ||
        imageData?.format?.small?.url ||
        imageData?.formats?.large?.url ||
        imageData?.format?.large?.url ||
        imageData?.url;
      console.log(`Mobile image selected: ${selectedUrl}`);
      console.log('Available mobile formats:', imageData?.formats);
    }

    // Desktop image selection logic
    if (!isMobile) {
      if (prefferedSize === 'large') {
        selectedUrl =
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url;
      } else if (prefferedSize === 'medium') {
        selectedUrl =
          imageData?.format?.medium?.url ||
          imageData?.formats?.medium?.url ||
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url;
      } else if (prefferedSize === 'small') {
        selectedUrl =
          imageData?.format?.small?.url ||
          imageData?.formats?.small?.url ||
          imageData?.format?.medium?.url ||
          imageData?.formats?.medium?.url ||
          imageData?.format?.large?.url ||
          imageData?.formats?.large?.url ||
          imageData?.url;
      } else {
        selectedUrl = imageData?.url;
      }
      console.log(`Desktop image selected: ${selectedUrl}`);
      console.log('Available desktop formats:', imageData?.formats);
    }

    return selectedUrl;
  };

  // Dynamic styles for mobile image based on mobileObjectFit prop
  const mobileImageStyle = {
    objectFit: mobileObjectFit,
    backgroundColor: mobileObjectFit === 'contain' ? '#000' : 'transparent',
  };

  return (
    <>
      {/* Desktop Image */}
      <div className={mobileData ? styles.desktop_only : ''}>
        {!isDesktopImageLoaded && data?.formats?.thumbnail?.url && (
          <Image
            src={data.formats.thumbnail.url}
            alt={data?.alternativeText || 'Hero image'}
            width={width}
            height={height}
            fill={fill}
            className={classNames(mainClass, 'blur')}
            priority={priority}
            loading={loading}
            unoptimized={unoptimized}
          />
        )}
        <Image
          src={getImageUrl(data, false)}
          width={width}
          height={height}
          fill={fill}
          alt={data?.alternativeText || 'Hero Image'}
          className={classNames(
            mainClass,
            isDesktopImageLoaded ? styles.image_visible : styles.image_hidden,
          )}
          quality={quality}
          priority={priority}
          loading={loading}
          unoptimized={unoptimized}
          onLoad={() => setIsDesktopImageLoaded(true)}
        />
      </div>

      {/* Mobile Image */}
      {mobileData && (
        <div className={styles.mobile_tablet_only}>
          {!isMobileImageLoaded && mobileData?.formats?.thumbnail?.url && (
            <Image
              src={mobileData.formats.thumbnail.url}
              alt={mobileData?.alternativeText || 'Mobile hero image'}
              width={width}
              height={height}
              fill={fill}
              className={classNames(mainClass, 'blur')}
              priority={priority}
              loading={loading}
              unoptimized={unoptimized}
            />
          )}
          <Image
            src={getImageUrl(mobileData, true)}
            width={width}
            height={height}
            fill={fill}
            alt={mobileData?.alternativeText || 'Mobile Hero Image'}
            className={classNames(
              mainClass,
              styles.mobile_image,
              isMobileImageLoaded ? styles.image_visible : styles.image_hidden,
            )}
            style={mobileImageStyle}
            quality={quality}
            priority={priority}
            loading={loading}
            unoptimized={unoptimized}
            onLoad={() => setIsMobileImageLoaded(true)}
          />
        </div>
      )}
    </>
  );
}
