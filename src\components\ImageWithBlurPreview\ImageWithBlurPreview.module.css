@value breakpoints: "@styles/breakpoints.module.css";
@value breakpoint-sm-450, breakpoint-md-767, breakpoint-md, breakpoint-xl-1800 from breakpoints;

.image_hidden {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.image_visible {
  opacity: 1;
}

.mobile_image {
  /* Default styles - will be overridden by inline styles from component */
  object-fit: cover;
  object-position: center center;

  /* Ensure smooth transitions */
  transition: opacity 0.5s ease-in-out;
}

/* Show desktop images only on desktop screens when mobile image is available */
.desktop_only {
  display: block;
  
  @media screen and (max-width: 1023px) {
    display: none !important;
  }
}

/* Show mobile images only on mobile and tablet screens */
.mobile_tablet_only {
  display: none;

  @media screen and (max-width: 1023px) {
    display: block;
  }
}
