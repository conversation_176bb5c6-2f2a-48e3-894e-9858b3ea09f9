'use client';

import { Container } from 'react-bootstrap';
import styles from './EbooksHeroSection.module.css';
import Heading from '@components/Heading';
import ImageWithBlurPreview from '@components/ImageWithBlurPreview';

export default function EbooksHeroSection({ heroData }: any) {
  return (
    <>
      {heroData?.title && (
        <Container fluid className={styles.main_container}>
          <ImageWithBlurPreview
            data={heroData?.image?.data?.attributes}
            mobileData={heroData?.mobile_image?.data?.attributes}
            fill={true}
            quality={95}
            priority={true}
            loading="eager"
            mainClass={styles.background_image}
          />

          <Heading
            className={styles.title}
            headingType="h1"
            title={heroData?.title}
          />
        </Container>
      )}
    </>
  );
}
